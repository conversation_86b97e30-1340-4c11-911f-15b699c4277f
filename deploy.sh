#!/bin/bash

# AQUESA Leak Detection System - Docker Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is installed and running
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are available"
}

# Function to setup environment
setup_environment() {
    print_status "Setting up environment..."
    
    if [ ! -f ".env.docker" ]; then
        print_error ".env.docker file not found. Please create it from .env.docker.example"
        exit 1
    fi
    
    # Copy Docker environment file
    cp .env.docker .env
    print_success "Environment configured"
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p models
    mkdir -p backups
    mkdir -p nginx
    
    print_success "Directories created"
}

# Function to build Docker images
build_images() {
    print_status "Building Docker images..."
    
    docker-compose build --no-cache
    
    print_success "Docker images built successfully"
}

# Function to start services
start_services() {
    local profile=${1:-""}
    
    print_status "Starting AQUESA services..."
    
    if [ -n "$profile" ]; then
        docker-compose --profile "$profile" up -d
    else
        docker-compose up -d
    fi
    
    print_success "Services started successfully"
}

# Function to show service status
show_status() {
    print_status "Service status:"
    docker-compose ps
    
    print_status "Service logs (last 20 lines):"
    docker-compose logs --tail=20
}

# Function to show service URLs
show_urls() {
    local api_port=$(grep "API_PORT" .env.docker | cut -d'=' -f2 | tr -d ' ')
    local frontend_port=$(grep "FRONTEND_PORT" .env.docker | cut -d'=' -f2 | tr -d ' ')
    
    api_port=${api_port:-8000}
    frontend_port=${frontend_port:-3000}
    
    echo ""
    print_success "🎉 AQUESA is now running!"
    echo ""
    echo "📡 API Server: http://localhost:$api_port"
    echo "🌐 Frontend: http://localhost:$frontend_port"
    echo "📊 API Documentation: http://localhost:$api_port/docs"
    echo ""
    print_status "To stop services: ./deploy.sh stop"
    print_status "To view logs: ./deploy.sh logs"
    print_status "To restart: ./deploy.sh restart"
}

# Function to stop services
stop_services() {
    print_status "Stopping AQUESA services..."
    docker-compose down
    print_success "Services stopped"
}

# Function to restart services
restart_services() {
    print_status "Restarting AQUESA services..."
    docker-compose restart
    print_success "Services restarted"
}

# Function to show logs
show_logs() {
    local service=${1:-""}
    
    if [ -n "$service" ]; then
        docker-compose logs -f "$service"
    else
        docker-compose logs -f
    fi
}

# Function to clean up
cleanup() {
    print_status "Cleaning up Docker resources..."
    
    docker-compose down -v
    docker system prune -f
    
    print_success "Cleanup completed"
}

# Main function
main() {
    local command=${1:-"deploy"}
    
    echo "🚀 AQUESA Docker Deployment Script"
    echo "=================================="
    
    case $command in
        "deploy"|"start")
            check_docker
            setup_environment
            create_directories
            build_images
            start_services
            sleep 5
            show_status
            show_urls
            ;;
        "dev")
            check_docker
            setup_environment
            create_directories
            print_status "Starting development environment..."
            docker-compose -f docker-compose.dev.yml up -d
            sleep 5
            show_status
            show_urls
            ;;
        "with-mongodb")
            check_docker
            setup_environment
            create_directories
            build_images
            start_services "with-mongodb"
            sleep 10
            show_status
            show_urls
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            sleep 5
            show_status
            ;;
        "logs")
            show_logs $2
            ;;
        "status")
            show_status
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  deploy, start    - Deploy AQUESA (default)"
            echo "  dev             - Start development environment"
            echo "  with-mongodb    - Deploy with local MongoDB"
            echo "  stop            - Stop all services"
            echo "  restart         - Restart all services"
            echo "  logs [service]  - Show logs (optionally for specific service)"
            echo "  status          - Show service status"
            echo "  cleanup         - Stop services and clean up Docker resources"
            echo "  help            - Show this help message"
            ;;
        *)
            print_error "Unknown command: $command"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
