/* AQUESA Leak Detection System Styles */

:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

#status-indicator {
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

#status-indicator.connected {
    color: var(--success-color);
}

#status-indicator.disconnected {
    color: var(--danger-color);
}

#status-indicator.connecting {
    color: var(--warning-color);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Statistics Cards */
.bg-primary .card-body {
    background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
}

.bg-danger .card-body {
    background: linear-gradient(135deg, #dc3545 0%, #b02a37 100%);
}

.bg-warning .card-body {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #000 !important;
}

.bg-success .card-body {
    background: linear-gradient(135deg, #198754 0%, #146c43 100%);
}

/* Tables */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

.leak-badge {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
}

.leak-badge.flood {
    background-color: var(--danger-color);
}

.leak-badge.threshold {
    background-color: var(--warning-color);
    color: #000;
}

.leak-badge.tap_leakage {
    background-color: var(--warning-color);
    color: #000;
}

.leak-badge.normal {
    background-color: var(--success-color);
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-group .btn {
    transform: none;
}

.btn-group .btn:hover {
    transform: none;
    box-shadow: none;
}

/* Tabs */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
    transition: all 0.2s ease-in-out;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: white;
    border-color: var(--primary-color) var(--primary-color) white;
    border-width: 0 0 3px 0;
    border-style: solid;
}

/* Forms */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(13, 202, 240, 0.1);
    color: #055160;
}

/* Modals */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 10px 10px;
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: 6px !important;
    }
    
    .nav-tabs .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from { transform: translateX(-10px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Status Indicators */
.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--danger-color);
}

.status-warning {
    color: var(--warning-color);
}

/* Utility Classes */
.text-truncate-custom {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.cursor-pointer {
    cursor: pointer;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-start-danger {
    border-left: 4px solid var(--danger-color) !important;
}

.border-start-warning {
    border-left: 4px solid var(--warning-color) !important;
}
