# AQUESA Environment Configuration Guide

This document explains how to configure the AQUESA Leak Detection System using environment variables.

## Quick Start

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file** with your specific configuration values

3. **Start the system:**
   ```bash
   python start_with_env.py
   ```

## Environment Variables

### Database Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `MONGO_URI` | `mongodb+srv://...` | MongoDB connection string |
| `DB_NAME` | `aquesa_management` | Database name |

### Server Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `API_HOST` | `0.0.0.0` | API server host |
| `API_PORT` | `8000` | API server port |
| `API_RELOAD` | `true` | Enable auto-reload for development |
| `FRONTEND_PORT` | `3000` | Frontend server port |
| `API_BASE_URL` | `http://localhost:8000/api` | API base URL for frontend |

### Monitoring Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `WINDOW_SIZE` | `10` | Historical data window (days) |
| `HOURLY_THRESHOLD` | `1.2` | Hourly leak detection threshold |
| `DAILY_THRESHOLD` | `1.2` | Daily leak detection threshold |
| `MONITOR_INTERVAL` | `10` | Monitoring check interval (seconds) |
| `RETRAIN_INTERVAL` | `3600` | Model retraining interval (seconds) |

### Leak Detection Parameters

| Variable | Default | Description |
|----------|---------|-------------|
| `FORGOTTEN_TAP_THRESHOLD` | `0.5` | Minimum flow for forgotten tap (L/min) |
| `FORGOTTEN_TAP_DURATION` | `30` | Duration for forgotten tap detection (minutes) |
| `TAP_LEAKAGE_THRESHOLD` | `0.05` | Tap leakage detection threshold |
| `MODEL_FILE` | `isolation_forest_model.pkl` | ML model file path |

### Security Settings

| Variable | Default | Description |
|----------|---------|-------------|
| `CORS_ORIGINS` | `http://localhost:3000,...` | Allowed CORS origins |
| `CORS_ALLOW_ALL` | `true` | Allow all origins (development only) |

### Development Settings

| Variable | Default | Description |
|----------|---------|-------------|
| `ENVIRONMENT` | `development` | Environment mode |
| `DEBUG` | `true` | Enable debug mode |
| `USE_MOCK_DATA` | `false` | Use mock data instead of database |
| `LOG_LEVEL` | `INFO` | Logging level |
| `VERBOSE_LOGGING` | `false` | Enable verbose logging |

### Notification Settings

| Variable | Default | Description |
|----------|---------|-------------|
| `EMAIL_NOTIFICATIONS` | `false` | Enable email notifications |
| `SMTP_SERVER` | `smtp.gmail.com` | SMTP server |
| `SMTP_PORT` | `587` | SMTP port |
| `EMAIL_USERNAME` | `` | Email username |
| `EMAIL_PASSWORD` | `` | Email password |
| `NOTIFICATION_EMAILS` | `` | Comma-separated notification emails |

### Performance Settings

| Variable | Default | Description |
|----------|---------|-------------|
| `BATCH_SIZE` | `1000` | Processing batch size |
| `DB_MIN_POOL_SIZE` | `1` | Minimum database connections |
| `DB_MAX_POOL_SIZE` | `10` | Maximum database connections |
| `ENABLE_CACHING` | `true` | Enable caching |
| `CACHE_TTL` | `300` | Cache time-to-live (seconds) |

### Backup and Maintenance

| Variable | Default | Description |
|----------|---------|-------------|
| `AUTO_BACKUP` | `false` | Enable automatic backups |
| `BACKUP_DIR` | `./backups` | Backup directory |
| `DATA_RETENTION_DAYS` | `365` | Data retention period (days) |

## Configuration Examples

### Development Environment
```env
ENVIRONMENT=development
DEBUG=true
API_RELOAD=true
USE_MOCK_DATA=false
LOG_LEVEL=DEBUG
VERBOSE_LOGGING=true
```

### Production Environment
```env
ENVIRONMENT=production
DEBUG=false
API_RELOAD=false
USE_MOCK_DATA=false
LOG_LEVEL=INFO
VERBOSE_LOGGING=false
CORS_ALLOW_ALL=false
CORS_ORIGINS=https://yourdomain.com
```

### Testing Environment
```env
ENVIRONMENT=testing
DEBUG=true
USE_MOCK_DATA=true
LOG_LEVEL=DEBUG
EMAIL_NOTIFICATIONS=false
```

## Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use strong passwords** for database connections
3. **Limit CORS origins** in production
4. **Use environment-specific configurations**
5. **Regularly rotate credentials**

## Startup Scripts

### Using Environment Configuration
```bash
python start_with_env.py
```

### Traditional Startup (without environment)
```bash
python start_aquesa_demo.py
```

### Manual Startup
```bash
# Start API server
uvicorn app.main:app --host 0.0.0.0 --port 8000

# Start frontend (in another terminal)
cd frontend
python server.py --port 3000
```

## Troubleshooting

### Common Issues

1. **Port already in use:**
   - Change `API_PORT` or `FRONTEND_PORT` in `.env`
   - Kill existing processes using the ports

2. **Database connection failed:**
   - Check `MONGO_URI` in `.env`
   - Verify network connectivity
   - Check database credentials

3. **Frontend can't connect to API:**
   - Verify `API_BASE_URL` in `.env`
   - Check CORS configuration
   - Ensure API server is running

### Environment Validation

The system will validate environment variables on startup and show warnings for:
- Missing required variables
- Invalid values
- Deprecated settings

## Migration from Hardcoded Configuration

If you're upgrading from a version with hardcoded configuration:

1. Create `.env` file from `.env.example`
2. Move your custom values to `.env`
3. Update startup scripts to use `start_with_env.py`
4. Test the configuration thoroughly
