import pandas as pd
import numpy as np

def preprocess_data(data):
    """Clean and prepare data for analysis"""
    if not isinstance(data, pd.DataFrame) or data.empty:
        return pd.DataFrame()

    # Clean timestamps
    data["datatime"] = pd.to_datetime(data["datatime"], errors="coerce")
    data = data.dropna(subset=["datatime"])
    data = data[data["datatime"] >= "2025-02-11"]

    # Extract consumption values
    if "data" not in data.columns:
        return pd.DataFrame()
    
    data["data.evt.csm"] = data["data"].apply(
        lambda x: x.get("evt", {}).get("csm", np.nan) if isinstance(x, dict) else np.nan
    )
    data = data.dropna(subset=["data.evt.csm"])
    data = data[data["data.evt.csm"] >= 0]

    # Remove outliers (3σ rule)
    if not data.empty:
        mean_csm = data["data.evt.csm"].mean()
        std_csm = data["data.evt.csm"].std()
        data = data[(data["data.evt.csm"] <= mean_csm + 3 * std_csm)]

    # Create time features
    data.loc[:, "hour"] = data["datatime"].dt.hour
    data.loc[:, "day_of_week"] = data["datatime"].dt.dayofweek
    data.loc[:, "date"] = data["datatime"].dt.date

    return data