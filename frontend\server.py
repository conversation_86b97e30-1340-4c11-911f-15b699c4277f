#!/usr/bin/env python3
"""
Simple HTTP server to serve the AQUESA frontend.
This serves the static files and handles CORS for API calls.
"""

import http.server
import socketserver
import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """HTTP request handler with CORS support."""

    def end_headers(self):
        """Add CORS headers to all responses."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()

    def do_OPTIONS(self):
        """Handle preflight OPTIONS requests."""
        self.send_response(200)
        self.end_headers()

    def log_message(self, format, *args):
        """Custom log format."""
        print(f"[{self.log_date_time_string()}] {format % args}")

def serve_frontend(port=None):
    """
    Serve the AQUESA frontend on the specified port.

    Args:
        port: Port to serve on (default: from environment or 3000)
    """
    # Use environment variable or default port
    if port is None:
        port = int(os.getenv("FRONTEND_PORT", "3000"))

    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)

    # Check if required files exist
    required_files = ['index.html', 'styles.css', 'app.js']
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
        print("❌ Error: Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print(f"   Current directory: {os.getcwd()}")
        print(f"   Available files: {os.listdir('.')}")
        sys.exit(1)

    print("✅ All required files found")
    
    try:
        with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
            print(f"🚀 AQUESA Frontend Server Starting...")
            print(f"📁 Serving directory: {os.getcwd()}")
            print(f"🌐 Server running at: http://localhost:{port}")
            print(f"🔗 Open in browser: http://localhost:{port}")
            api_port = os.getenv("API_PORT", "8000")
            print(f"⚠️  Make sure your AQUESA API is running on http://localhost:{api_port}")
            print(f"🛑 Press Ctrl+C to stop the server")
            print("-" * 60)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Error: Port {port} is already in use")
            print(f"   Try a different port: python server.py --port {port + 1}")
        else:
            print(f"❌ Error starting server: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def main():
    """Main function with command line argument parsing."""
    import argparse

    parser = argparse.ArgumentParser(description='AQUESA Frontend Server')
    parser.add_argument('--port', '-p', type=int, default=3000,
                       help='Port to serve on (default: 3000)')

    args = parser.parse_args()

    serve_frontend(args.port)

if __name__ == "__main__":
    main()
