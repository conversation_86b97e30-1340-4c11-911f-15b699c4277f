# AQUESA Docker Deployment Guide

This guide explains how to deploy the AQUESA Leak Detection System using Docker containers.

## Prerequisites

- Docker Desktop (Windows/Mac) or Docker Engine (Linux)
- Docker Compose
- At least 4GB RAM available for containers
- MongoDB Atlas account or local MongoDB instance

## Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd aquesa_leak_detection
```

### 2. Configure Environment

```bash
# Copy the Docker environment template
cp .env.docker .env.docker.local

# Edit the configuration (especially MongoDB URI)
nano .env.docker.local
```

### 3. Deploy

**Linux/Mac:**
```bash
chmod +x deploy.sh
./deploy.sh deploy
```

**Windows:**
```cmd
deploy.bat deploy
```

### 4. Access the Application

- **Frontend:** http://localhost:3000
- **API:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs

## Deployment Options

### Standard Deployment

Deploys API and Frontend with external MongoDB:

```bash
./deploy.sh deploy
```

### Development Environment

Includes hot-reload and development settings:

```bash
./deploy.sh dev
```

### With Local MongoDB

Includes a local MongoDB container:

```bash
./deploy.sh with-mongodb
```

### With Full Stack (MongoDB + Redis + Nginx)

```bash
docker-compose --profile with-mongodb --profile with-redis --profile with-nginx up -d
```

## Docker Services

### Core Services

| Service | Description | Port | Health Check |
|---------|-------------|------|--------------|
| `aquesa-api` | Main API backend | 8000 | `/` endpoint |
| `aquesa-frontend` | Web interface | 3000 | `/` endpoint |

### Optional Services

| Service | Description | Port | Profile |
|---------|-------------|------|---------|
| `mongodb` | Local MongoDB | 27017 | `with-mongodb` |
| `redis` | Caching layer | 6379 | `with-redis` |
| `nginx` | Reverse proxy | 80/443 | `with-nginx` |

## Configuration

### Environment Variables

Key variables in `.env.docker`:

```env
# Database
MONGO_URI=********************************************************************************
DB_NAME=aquesa_management

# Ports
API_PORT=8000
FRONTEND_PORT=3000

# Monitoring
MONITOR_INTERVAL=10
RETRAIN_INTERVAL=3600

# Security
CORS_ALLOW_ALL=true
DEBUG=false
```

### Volume Mounts

| Volume | Purpose | Host Path |
|--------|---------|-----------|
| `./models` | ML models | `/app/models` |
| `./logs` | Application logs | `/app/logs` |
| `./backups` | Data backups | `/app/backups` |

## Management Commands

### Service Control

```bash
# Start services
./deploy.sh start

# Stop services
./deploy.sh stop

# Restart services
./deploy.sh restart

# View status
./deploy.sh status
```

### Monitoring

```bash
# View all logs
./deploy.sh logs

# View specific service logs
./deploy.sh logs aquesa-api
./deploy.sh logs aquesa-frontend

# Follow logs in real-time
docker-compose logs -f
```

### Maintenance

```bash
# Clean up resources
./deploy.sh cleanup

# Rebuild images
docker-compose build --no-cache

# Update containers
docker-compose pull
docker-compose up -d
```

## Production Deployment

### 1. Security Configuration

```env
# .env.docker for production
ENVIRONMENT=production
DEBUG=false
CORS_ALLOW_ALL=false
CORS_ORIGINS=https://yourdomain.com
```

### 2. SSL/HTTPS Setup

1. Place SSL certificates in `nginx/ssl/`
2. Update `nginx/nginx.conf` with your domain
3. Enable HTTPS server block

### 3. Resource Limits

Add to `docker-compose.yml`:

```yaml
services:
  aquesa-api:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

### 4. Health Monitoring

```bash
# Check container health
docker-compose ps

# Monitor resource usage
docker stats

# View system events
docker events
```

## Scaling

### Horizontal Scaling

```yaml
# docker-compose.override.yml
services:
  aquesa-api:
    scale: 3
  
  nginx:
    depends_on:
      - aquesa-api
```

### Load Balancing

Update `nginx/nginx.conf`:

```nginx
upstream aquesa_api {
    server aquesa-api_1:8000;
    server aquesa-api_2:8000;
    server aquesa-api_3:8000;
}
```

## Backup and Recovery

### Database Backup

```bash
# MongoDB backup (if using local MongoDB)
docker-compose exec mongodb mongodump --out /data/backup

# Copy backup from container
docker cp aquesa-mongodb:/data/backup ./backups/
```

### Application Data

```bash
# Backup volumes
docker run --rm -v aquesa_mongodb_data:/data -v $(pwd)/backups:/backup alpine tar czf /backup/mongodb-backup.tar.gz -C /data .
```

## Troubleshooting

### Common Issues

1. **Port conflicts:**
   ```bash
   # Change ports in .env.docker
   API_PORT=8001
   FRONTEND_PORT=3001
   ```

2. **Memory issues:**
   ```bash
   # Increase Docker memory limit
   # Docker Desktop > Settings > Resources > Memory
   ```

3. **Database connection:**
   ```bash
   # Check MongoDB URI in .env.docker
   # Verify network connectivity
   docker-compose logs aquesa-api
   ```

### Debug Mode

```bash
# Enable debug logging
echo "DEBUG=true" >> .env.docker
echo "LOG_LEVEL=DEBUG" >> .env.docker

# Restart with debug
./deploy.sh restart
```

### Container Shell Access

```bash
# Access API container
docker-compose exec aquesa-api bash

# Access frontend container
docker-compose exec aquesa-frontend bash

# Access MongoDB container
docker-compose exec mongodb mongo
```

## Performance Optimization

### 1. Image Optimization

- Use multi-stage builds
- Minimize layer count
- Use `.dockerignore`

### 2. Resource Allocation

```yaml
services:
  aquesa-api:
    cpus: 1.0
    mem_limit: 1g
    mem_reservation: 512m
```

### 3. Caching

- Enable Redis for caching
- Use Docker layer caching
- Implement application-level caching

## Monitoring and Logging

### Log Aggregation

```bash
# Centralized logging
docker-compose logs --follow --timestamps
```

### Metrics Collection

```yaml
# Add monitoring stack
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
  
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
```

## Security Best Practices

1. **Use non-root users in containers**
2. **Scan images for vulnerabilities**
3. **Limit container capabilities**
4. **Use secrets management**
5. **Enable container security scanning**

```bash
# Security scan
docker scan aquesa-api:latest
```

## Migration from Development

1. Update environment variables
2. Configure production database
3. Set up SSL certificates
4. Configure monitoring
5. Test deployment thoroughly

## Support

For issues and questions:

1. Check logs: `./deploy.sh logs`
2. Verify configuration: `./deploy.sh status`
3. Review documentation
4. Contact support team
