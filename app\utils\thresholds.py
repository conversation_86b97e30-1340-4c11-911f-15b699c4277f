from collections import defaultdict
from datetime import timedelta
from app.core.config import settings

max_csm_by_hour = defaultdict(float)
max_csm_by_day = defaultdict(float)

def update_max_csm(data):
    """Update sliding window maxima for thresholds"""
    if data.empty:
        return

    latest_time = data["datatime"].max()
    window_start = latest_time - timedelta(days=settings.WINDOW_SIZE)
    window_data = data[data["datatime"] >= window_start]

    # Update hourly maxima
    for hour in range(24):
        hour_data = window_data[window_data["hour"] == hour]
        max_csm = hour_data["data.evt.csm"].max() if not hour_data.empty else 0.0
        max_csm_by_hour[hour] = max_csm

    # Update daily maxima
    current_date = window_start.date()
    while current_date <= latest_time.date():
        date_data = window_data[window_data["date"] == current_date]
        max_csm = date_data["data.evt.csm"].max() if not date_data.empty else 0.0
        max_csm_by_day[current_date] = max_csm
        current_date += timedelta(days=1)