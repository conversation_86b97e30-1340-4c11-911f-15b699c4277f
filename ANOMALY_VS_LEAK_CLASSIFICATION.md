# Anomaly vs Leak Classification in AQUESA

## Overview

This document explains the updated classification system in the AQUESA leak detection system, where **anomalies are NOT considered as leakage**.

## Classification Logic

### Current System (Updated)

The system now distinguishes between anomalies and actual leaks:

1. **Anomaly Detection**: Uses Isolation Forest to identify unusual consumption patterns
2. **Threshold Detection**: Identifies consumption that exceeds historical thresholds
3. **Leak Classification**: Only certain combinations are considered actual leaks

### Leak Types

| Type | Condition | Description | Severity |
|------|-----------|-------------|----------|
| `flood` | Anomaly + Threshold | Critical leak requiring immediate attention | High |
| `threshold` | Threshold only | Consumption exceeds historical limits | Medium |
| `tap_leakage` | Continuous low flow | Sustained low-level consumption | Medium |
| ~~`anomaly`~~ | ~~Anomaly only~~ | ~~**REMOVED** - No longer considered a leak~~ | ~~Low~~ |

### Key Changes

#### ✅ What IS Considered a Leak:
- **Flood leaks**: Anomalous consumption that also exceeds thresholds
- **Threshold violations**: High consumption exceeding historical limits
- **Tap leakage**: Continuous low-level flow patterns

#### ❌ What is NOT Considered a Leak:
- **Standalone anomalies**: Unusual patterns that don't exceed thresholds
- **Normal consumption**: Regular usage patterns

## Technical Implementation

### Backend Changes

**File**: `app/utils/anomaly_detection.py`

```python
def detect_leakage(new_data, model, peak_hours):
    """Leak detection that excludes standalone anomalies"""
    
    # 1. Detect anomalies (for flood detection only)
    anomalies_mask = (model.predict(features) == -1)
    
    # 2. Detect threshold violations
    threshold_mask = (consumption > thresholds)
    
    # 3. Classify leaks
    flood_mask = anomalies_mask & threshold_mask  # Both conditions
    
    # Only return flood and threshold violations as leaks
    leaks = data[data["type"].isin(["flood", "threshold"])]
    
    # Standalone anomalies are excluded
    return leaks
```

### Frontend Changes

**File**: `frontend/styles.css`
- Added styling for `threshold` leak type
- Removed references to `anomaly` type

**File**: `frontend/app.js`
- Updated leak type chart to show: Threshold, Tap Leakage, Flood
- Enhanced statistics to track different leak types
- Improved leak type formatting

## Rationale

### Why Anomalies Alone Are Not Leaks

1. **False Positives**: Anomalies can be caused by legitimate usage patterns
2. **Context Matters**: Unusual doesn't always mean problematic
3. **Threshold Validation**: Real leaks typically exceed normal consumption levels
4. **Operational Efficiency**: Reduces alert fatigue from non-critical events

### Benefits of New Classification

1. **Reduced False Alarms**: Only actionable events are flagged as leaks
2. **Better Prioritization**: Clear severity levels (flood > threshold > tap)
3. **Improved Accuracy**: Combines multiple detection methods for validation
4. **Cleaner UI**: Frontend shows only relevant leak types

## Monitoring Impact

### Real-time Detection
- Anomalies are still detected but not reported as leaks
- Only threshold violations and floods trigger leak alerts
- Tap leakage detection remains unchanged

### Historical Analysis
- Anomaly data is still available for analysis
- Leak statistics focus on actionable events
- Trend analysis shows actual leak patterns

## Configuration

The system behavior is controlled by these settings:

```env
# Threshold multipliers for leak detection
HOURLY_THRESHOLD=1.2    # 20% above historical hourly max
DAILY_THRESHOLD=1.2     # 20% above historical daily max

# Tap leakage detection
TAP_LEAKAGE_THRESHOLD=0.05
FORGOTTEN_TAP_THRESHOLD=0.5
FORGOTTEN_TAP_DURATION=30
```

## Migration Notes

### Existing Data
- Historical anomaly-only events are no longer classified as leaks
- Flood and threshold events remain unchanged
- No data loss - anomaly information is preserved

### API Responses
- `/api/leaks` endpoint now returns fewer events (more accurate)
- Leak types are limited to: `flood`, `threshold`, `tap_leakage`
- Response format remains the same

### Frontend Display
- Leak tables show only actionable events
- Charts reflect new classification system
- Statistics are more meaningful

## Summary

The updated AQUESA system provides more accurate leak detection by:
- Distinguishing between anomalies and actual leaks
- Requiring threshold validation for most leak classifications
- Focusing alerts on actionable events
- Maintaining anomaly detection for flood identification

This change improves system reliability and reduces false positive alerts while maintaining comprehensive water consumption monitoring.
