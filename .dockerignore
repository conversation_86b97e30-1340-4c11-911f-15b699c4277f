# AQUESA Docker Ignore File

# Environment files (will be handled separately)
.env
.env.local
.env.*.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
myenv/
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation
*.md
docs/

# Test files
tests/
test_*.py
*_test.py

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.tmp/

# Backup files
*.backup
backups/

# Data files (should be mounted as volumes)
*.csv
*.json
*.parquet

# Model files (should be mounted as volumes)
*.pkl
*.joblib
*.h5
*.model

# Docker files (not needed in container)
Dockerfile*
docker-compose*.yml
.dockerignore

# Development scripts
start_*.py
quick_start.*
test_*.py

# Node modules (if any)
node_modules/
