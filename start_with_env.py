#!/usr/bin/env python3
"""
Start AQUESA servers using environment configuration from .env file.
"""

import subprocess
import sys
import time
import os
import webbrowser
from threading import Timer
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_env_config():
    """Get configuration from environment variables."""
    return {
        'api_host': os.getenv('API_HOST', '0.0.0.0'),
        'api_port': int(os.getenv('API_PORT', '8000')),
        'api_reload': os.getenv('API_RELOAD', 'true').lower() == 'true',
        'frontend_port': int(os.getenv('FRONTEND_PORT', '3000')),
        'environment': os.getenv('ENVIRONMENT', 'development'),
        'debug': os.getenv('DEBUG', 'true').lower() == 'true',
        'use_mock_data': os.getenv('USE_MOCK_DATA', 'false').lower() == 'true'
    }

def start_api_server(config):
    """Start the API server with environment configuration."""
    print("🚀 Starting AQUESA API Server...")
    print(f"   Environment: {config['environment']}")
    print(f"   Host: {config['api_host']}")
    print(f"   Port: {config['api_port']}")
    print(f"   Debug: {config['debug']}")
    print(f"   Auto-reload: {config['api_reload']}")
    
    # Choose which server to start based on configuration
    if config['use_mock_data']:
        print("   Using mock data server")
        cmd = [sys.executable, "test_api_server.py"]
    else:
        print("   Using production API server")
        cmd = [
            sys.executable, "-m", "uvicorn", "app.main:app",
            "--host", config['api_host'],
            "--port", str(config['api_port'])
        ]
        if config['api_reload']:
            cmd.append("--reload")
    
    api_process = subprocess.Popen(cmd, cwd=os.getcwd())
    return api_process

def start_frontend_server(config):
    """Start the frontend server with environment configuration."""
    print("🌐 Starting AQUESA Frontend Server...")
    print(f"   Port: {config['frontend_port']}")
    
    # Start the frontend server
    frontend_process = subprocess.Popen([
        sys.executable, "server.py", "--port", str(config['frontend_port'])
    ], cwd=os.path.join(os.getcwd(), "frontend"))
    
    return frontend_process

def open_browser(config):
    """Open the browser to the frontend URL."""
    url = f"http://localhost:{config['frontend_port']}"
    print(f"🌐 Opening browser: {url}")
    webbrowser.open(url)

def main():
    """Main function to start all servers."""
    print("🚀 AQUESA Environment-Based Startup")
    print("=" * 50)
    
    # Load configuration
    config = get_env_config()
    
    # Display configuration
    print("\n📋 Configuration:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    print("\n" + "=" * 50)
    
    try:
        # Start API server
        api_process = start_api_server(config)
        print(f"✅ API server started (PID: {api_process.pid})")
        
        # Wait for API to start
        print("⏳ Waiting for API server to initialize...")
        time.sleep(5)
        
        # Start frontend server
        frontend_process = start_frontend_server(config)
        print(f"✅ Frontend server started (PID: {frontend_process.pid})")
        
        # Wait for frontend to start
        print("⏳ Waiting for frontend server to initialize...")
        time.sleep(3)
        
        print("\n🎉 AQUESA is ready!")
        print(f"📡 API Server: http://{config['api_host']}:{config['api_port']}")
        print(f"🌐 Frontend: http://localhost:{config['frontend_port']}")
        print("\n🛑 Press Ctrl+C to stop all servers")
        print("=" * 50)
        
        # Open browser after 3 seconds
        Timer(3.0, lambda: open_browser(config)).start()
        
        # Wait for user to stop
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping servers...")
            
            # Stop processes
            api_process.terminate()
            frontend_process.terminate()
            
            # Wait for processes to stop
            api_process.wait()
            frontend_process.wait()
            
            print("✅ All servers stopped")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
