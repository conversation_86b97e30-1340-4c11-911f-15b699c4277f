@echo off
REM AQUESA Leak Detection System - Docker Deployment Script for Windows

setlocal enabledelayedexpansion

REM Function to print colored output (Windows doesn't support colors easily, so we'll use simple text)
set "INFO_PREFIX=[INFO]"
set "SUCCESS_PREFIX=[SUCCESS]"
set "WARNING_PREFIX=[WARNING]"
set "ERROR_PREFIX=[ERROR]"

REM Function to check if Docker is installed and running
:check_docker
echo %INFO_PREFIX% Checking Docker installation...

docker --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR_PREFIX% Docker is not installed. Please install Docker Desktop first.
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo %ERROR_PREFIX% Docker is not running. Please start Docker Desktop first.
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR_PREFIX% Docker Compose is not installed. Please install Docker Compose first.
    exit /b 1
)

echo %SUCCESS_PREFIX% Docker and Docker Compose are available
goto :eof

REM Function to setup environment
:setup_environment
echo %INFO_PREFIX% Setting up environment...

if not exist ".env.docker" (
    echo %ERROR_PREFIX% .env.docker file not found. Please create it from .env.docker.example
    exit /b 1
)

copy ".env.docker" ".env" >nul
echo %SUCCESS_PREFIX% Environment configured
goto :eof

REM Function to create necessary directories
:create_directories
echo %INFO_PREFIX% Creating necessary directories...

if not exist "logs" mkdir logs
if not exist "models" mkdir models
if not exist "backups" mkdir backups
if not exist "nginx" mkdir nginx

echo %SUCCESS_PREFIX% Directories created
goto :eof

REM Function to build Docker images
:build_images
echo %INFO_PREFIX% Building Docker images...

docker-compose build --no-cache
if errorlevel 1 (
    echo %ERROR_PREFIX% Failed to build Docker images
    exit /b 1
)

echo %SUCCESS_PREFIX% Docker images built successfully
goto :eof

REM Function to start services
:start_services
echo %INFO_PREFIX% Starting AQUESA services...

if "%~1"=="" (
    docker-compose up -d
) else (
    docker-compose --profile %~1 up -d
)

if errorlevel 1 (
    echo %ERROR_PREFIX% Failed to start services
    exit /b 1
)

echo %SUCCESS_PREFIX% Services started successfully
goto :eof

REM Function to show service status
:show_status
echo %INFO_PREFIX% Service status:
docker-compose ps

echo %INFO_PREFIX% Service logs (last 20 lines):
docker-compose logs --tail=20
goto :eof

REM Function to show service URLs
:show_urls
REM Read ports from .env.docker file (simplified approach)
set API_PORT=8000
set FRONTEND_PORT=3000

for /f "tokens=2 delims==" %%a in ('findstr "API_PORT" .env.docker 2^>nul') do set API_PORT=%%a
for /f "tokens=2 delims==" %%a in ('findstr "FRONTEND_PORT" .env.docker 2^>nul') do set FRONTEND_PORT=%%a

echo.
echo %SUCCESS_PREFIX% 🎉 AQUESA is now running!
echo.
echo 📡 API Server: http://localhost:%API_PORT%
echo 🌐 Frontend: http://localhost:%FRONTEND_PORT%
echo 📊 API Documentation: http://localhost:%API_PORT%/docs
echo.
echo %INFO_PREFIX% To stop services: deploy.bat stop
echo %INFO_PREFIX% To view logs: deploy.bat logs
echo %INFO_PREFIX% To restart: deploy.bat restart
goto :eof

REM Function to stop services
:stop_services
echo %INFO_PREFIX% Stopping AQUESA services...
docker-compose down
echo %SUCCESS_PREFIX% Services stopped
goto :eof

REM Function to restart services
:restart_services
echo %INFO_PREFIX% Restarting AQUESA services...
docker-compose restart
echo %SUCCESS_PREFIX% Services restarted
goto :eof

REM Function to show logs
:show_logs
if "%~1"=="" (
    docker-compose logs -f
) else (
    docker-compose logs -f %~1
)
goto :eof

REM Function to clean up
:cleanup
echo %INFO_PREFIX% Cleaning up Docker resources...
docker-compose down -v
docker system prune -f
echo %SUCCESS_PREFIX% Cleanup completed
goto :eof

REM Main function
:main
set command=%~1
if "%command%"=="" set command=deploy

echo 🚀 AQUESA Docker Deployment Script
echo ==================================

if "%command%"=="deploy" goto :deploy
if "%command%"=="start" goto :deploy
if "%command%"=="dev" goto :dev
if "%command%"=="with-mongodb" goto :with_mongodb
if "%command%"=="stop" goto :stop
if "%command%"=="restart" goto :restart
if "%command%"=="logs" goto :logs
if "%command%"=="status" goto :status
if "%command%"=="cleanup" goto :cleanup
if "%command%"=="help" goto :help
if "%command%"=="-h" goto :help
if "%command%"=="--help" goto :help

echo %ERROR_PREFIX% Unknown command: %command%
echo Use 'deploy.bat help' for usage information
exit /b 1

:deploy
call :check_docker
if errorlevel 1 exit /b 1
call :setup_environment
if errorlevel 1 exit /b 1
call :create_directories
call :build_images
if errorlevel 1 exit /b 1
call :start_services
timeout /t 5 /nobreak >nul
call :show_status
call :show_urls
goto :eof

:dev
call :check_docker
if errorlevel 1 exit /b 1
call :setup_environment
if errorlevel 1 exit /b 1
call :create_directories
echo %INFO_PREFIX% Starting development environment...
docker-compose -f docker-compose.dev.yml up -d
timeout /t 5 /nobreak >nul
call :show_status
call :show_urls
goto :eof

:with_mongodb
call :check_docker
if errorlevel 1 exit /b 1
call :setup_environment
if errorlevel 1 exit /b 1
call :create_directories
call :build_images
if errorlevel 1 exit /b 1
call :start_services with-mongodb
timeout /t 10 /nobreak >nul
call :show_status
call :show_urls
goto :eof

:stop
call :stop_services
goto :eof

:restart
call :restart_services
timeout /t 5 /nobreak >nul
call :show_status
goto :eof

:logs
call :show_logs %~2
goto :eof

:status
call :show_status
goto :eof

:cleanup
call :cleanup
goto :eof

:help
echo Usage: deploy.bat [command]
echo.
echo Commands:
echo   deploy, start    - Deploy AQUESA (default)
echo   dev             - Start development environment
echo   with-mongodb    - Deploy with local MongoDB
echo   stop            - Stop all services
echo   restart         - Restart all services
echo   logs [service]  - Show logs (optionally for specific service)
echo   status          - Show service status
echo   cleanup         - Stop services and clean up Docker resources
echo   help            - Show this help message
goto :eof

REM Call main function with all arguments
call :main %*
