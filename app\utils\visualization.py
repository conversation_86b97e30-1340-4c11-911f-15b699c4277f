import matplotlib.pyplot as plt

def identify_peak_hours(data, n_top=5):
    """Identify peak consumption hours based on statistical percentiles"""
    if data.empty:
        return []

    hourly_data = data.groupby("hour")["data.evt.csm"].mean().reset_index()
    top_hours = hourly_data.nlargest(n_top, "data.evt.csm")["hour"].tolist()

    plt.figure(figsize=(10, 4))
    plt.bar(hourly_data["hour"], hourly_data["data.evt.csm"], label='Average Consumption')
    plt.scatter(top_hours, hourly_data[hourly_data["hour"].isin(top_hours)]["data.evt.csm"], color='r', label='Peak Hours')
    plt.title("Average Consumption Pattern with Peak Hours")
    plt.xlabel("Hour of Day")
    plt.ylabel("Consumption")
    plt.legend()
    plt.grid(True)
    plt.savefig("consumption_pattern_with_peaks.png")
    plt.close()

    return top_hours